import { Drag<PERSON>ndEvent, KeyboardSensor, MouseSensor, TouchSensor, UniqueIdentifier, useSensor, useSensors } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { useCallback, useId, useMemo, useState } from "react";
import { IFieldTable } from "../../../validators/form/create";

interface UseCreateFormDragTableProps {
	fields: IFieldTable[];
	reorderFields: (oldIndex: number, newIndex: number) => void;
}

export const useCreateFormDragTable = ({ fields, reorderFields }: UseCreateFormDragTableProps) => {
	const sortableId = useId();
	const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);

	const dataIds = useMemo(() => fields.map((_, index) => `field-${index}`), [fields]);

	const sensors = useSensors(
		useSensor(MouseSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
		useSensor(TouchSensor, {
			activationConstraint: {
				delay: 200,
				tolerance: 5,
			},
		}),
		useSensor(KeyboardSensor, {})
	);

	const restrictToTableArea = useCallback((args: Parameters<typeof restrictToVerticalAxis>[0]) => {
		const restrictedVertical = restrictToVerticalAxis(args);
		return {
			...restrictedVertical,
			x: 0,
		};
	}, []);

	const handleDragStart = useCallback((event: { active: { id: UniqueIdentifier } }) => {
		setActiveId(event.active.id);
	}, []);

	const handleDragEnd = useCallback(
		(event: DragEndEvent) => {
			const { active, over } = event;
			setActiveId(null);

			if (active && over && active.id !== over.id) {
				const activeId = active.id as string;
				const overId = over.id as string;

				const oldIndex = parseInt(activeId.replace("field-", ""));
				const newIndex = parseInt(overId.replace("field-", ""));

				if (oldIndex !== newIndex && oldIndex >= 0 && newIndex >= 0 && oldIndex < fields.length && newIndex < fields.length) {
					reorderFields(oldIndex, newIndex);
				}
			}
		},
		[fields.length, reorderFields]
	);

	const handleDragCancel = useCallback(() => {
		setActiveId(null);
	}, []);

	return {
		sortableId,
		sensors,
		dataIds,
		activeId,
		restrictToTableArea,
		handleDragStart,
		handleDragEnd,
		handleDragCancel,
	};
};
