import { columns, IProdutoTableData } from "./columns";
import { DataTable } from "./data-table";

const data: IProdutoTableData[] = [
	{
		solicitado: 10,
		atendido: 2,
		saldo: 8,
		modelo: "LISA",
		acabamento: "BRANCO",
		largura: "82cm",
		altura: "205cm",
		situacao: "A",
		ambiente: "EXT",
		fechadura: "FECHADURA PADRÃO 40",
		dobradica: "DOBRADIÇA 3x2,5 CR",
		acessorios: "Concha",
	},
	{
		solicitado: 15,
		atendido: 2,
		saldo: 13,
		modelo: "LISA",
		acabamento: "BRANCO",
		largura: "100cm",
		altura: "205cm",
		situacao: "A",
		ambiente: "EXT",
		fechadura: "FECHADURA PADRÃO 40",
		dobradica: "DOBRADIÇA 3x2,5 CR",
		acessorios: "Concha",
	},
	{
		solicitado: 15,
		atendido: 2,
		saldo: 13,
		modelo: "LISA",
		acabamento: "BRANCO",
		largura: "100cm",
		altura: "205cm",
		situacao: "A",
		ambiente: "EXT",
		fechadura: "FECHADURA PADRÃO 40",
		dobradica: "DOBRADIÇA 3x2,5 CR",
		acessorios: "Concha",
	},
	{
		solicitado: 15,
		atendido: 2,
		saldo: 13,
		modelo: "LISA",
		acabamento: "BRANCO",
		largura: "100cm",
		altura: "205cm",
		situacao: "A",
		ambiente: "EXT",
		fechadura: "FECHADURA PADRÃO 40",
		dobradica: "DOBRADIÇA 3x2,5 CR",
		acessorios: "Concha",
	},
	{
		solicitado: 15,
		atendido: 2,
		saldo: 13,
		modelo: "LISA",
		acabamento: "BRANCO",
		largura: "100cm",
		altura: "205cm",
		situacao: "A",
		ambiente: "EXT",
		fechadura: "FECHADURA PADRÃO 40",
		dobradica: "DOBRADIÇA 3x2,5 CR",
		acessorios: "Concha",
	},
	{
		solicitado: 15,
		atendido: 2,
		saldo: 13,
		modelo: "LISA",
		acabamento: "BRANCO",
		largura: "100cm",
		altura: "205cm",
		situacao: "A",
		ambiente: "EXT",
		fechadura: "FECHADURA PADRÃO 40",
		dobradica: "DOBRADIÇA 3x2,5 CR",
		acessorios: "Concha",
	},
	{
		solicitado: 15,
		atendido: 2,
		saldo: 13,
		modelo: "LISA",
		acabamento: "BRANCO",
		largura: "100cm",
		altura: "205cm",
		situacao: "A",
		ambiente: "EXT",
		fechadura: "FECHADURA PADRÃO 40",
		dobradica: "DOBRADIÇA 3x2,5 CR",
		acessorios: "Concha",
	},
	{
		solicitado: 15,
		atendido: 2,
		saldo: 13,
		modelo: "LISA",
		acabamento: "BRANCO",
		largura: "100cm",
		altura: "210cm",
		situacao: "A",
		ambiente: "EXT",
		fechadura: "FECHADURA PADRÃO 40",
		dobradica: "DOBRADIÇA 3x2,5 CR",
		acessorios: "Concha",
	},
	{
		solicitado: 15,
		atendido: 2,
		saldo: 13,
		modelo: "LISA",
		acabamento: "BRANCO",
		largura: "100cm",
		altura: "205cm",
		situacao: "A",
		ambiente: "EXT",
		fechadura: "FECHADURA PADRÃO 40",
		dobradica: "DOBRADIÇA 3x2,5 CR",
		acessorios: "Concha",
	},
	{
		solicitado: 15,
		atendido: 2,
		saldo: 13,
		modelo: "LISA",
		acabamento: "BRANCO",
		largura: "100cm",
		altura: "205cm",
		situacao: "A",
		ambiente: "EXT",
		fechadura: "FECHADURA PADRÃO 40",
		dobradica: "DOBRADIÇA 3x2,5 CR",
		acessorios: "Concha",
	},
];

const Page: React.FC = () => {
	return (
		<>
			<header className="flex items-center justify-between h-[60px] border-b border-border">
				<h1 className="text-2xl font-semibold">Testes</h1>
			</header>
			<main className="flex flex-col gap-4">
				<DataTable columns={columns} data={data} tableId="produtos-table" />
			</main>
		</>
	);
};

export default Page;
