"use client";
import { useCreateFormDragTable } from "@/modules/inspection/hooks/form/create/drag-table.hook";
import { ICreateForm, IFieldTable } from "@/modules/inspection/validators/form/create";
import { Button } from "@/shared/components/shadcn/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { Plus } from "lucide-react";
import { useMemo, useState } from "react";
import { Control } from "react-hook-form";
import { createColumnsFieldForm } from "./columns";
import { FieldOptions } from "./field-options";
import { SortableRow } from "./sortable-row";

interface TableCreateFormFieldsProps {
	fields: IFieldTable[];
	control: Control<ICreateForm>;
	addField: (field: Omit<IFieldTable, "sequence">) => void;
	removeField: (index: number) => void;
	updateField: (index: number, field: Partial<IFieldTable>) => void;
	reorderFields: (oldIndex: number, newIndex: number) => void;
	isFieldTypeOptions: (typeId: number) => boolean;
}

export const TableCreateFormFields: React.FC<TableCreateFormFieldsProps> = ({
	fields,
	addField,
	removeField,
	reorderFields,
	isFieldTypeOptions,
	control,
}) => {
	const [editingRowIndex, setEditingRowIndex] = useState<number | null>(null);
	const [selectedRowIndex, setSelectedRowIndex] = useState<number | null>(null);

	const { sortableId, sensors, dataIds, restrictToTableArea, handleDragStart, handleDragEnd, handleDragCancel } = useCreateFormDragTable({
		fields,
		reorderFields,
	});

	const columns = useMemo(
		() =>
			createColumnsFieldForm({
				control,
				onDelete: (index: number) => removeField(index),
				onFieldFocus: (index: number) => setEditingRowIndex(index),
				onFieldBlur: () => setEditingRowIndex(null),
			}),
		[control, removeField]
	);

	const table = useReactTable({
		data: fields,
		columns,
		getCoreRowModel: getCoreRowModel(),
		getRowId: (_, index) => `field-${index}`,
	});

	const getFieldOptionsToShow = () => {
		if (fields.length === 1 && isFieldTypeOptions(fields[0].typeId)) return { fieldIndex: 0, field: fields[0] };
		if (selectedRowIndex !== null && fields[selectedRowIndex] && isFieldTypeOptions(fields[selectedRowIndex].typeId))
			return { fieldIndex: selectedRowIndex, field: fields[selectedRowIndex] };
		if (editingRowIndex !== null && fields[editingRowIndex] && isFieldTypeOptions(fields[editingRowIndex].typeId))
			return { fieldIndex: editingRowIndex, field: fields[editingRowIndex] };
		return null;
	};

	const fieldOptionsToShow = getFieldOptionsToShow();

	return (
		<div className="flex flex-col gap-4 mt-2">
			<div>
				<Button
					variant="outline"
					onClick={e => {
						e.preventDefault();
						addField({
							fieldId: undefined,
							groupTitle: "",
							nickname: "",
							required: false,
							group: 1,
							typeId: 1,
							measureId: 1,
							biFilter: false,
						});
					}}
				>
					Adicionar campo
				</Button>
			</div>
			<div className="overflow-hidden w-full rounded-lg border">
				<DndContext
					collisionDetection={closestCenter}
					modifiers={[restrictToTableArea]}
					onDragStart={handleDragStart}
					onDragEnd={handleDragEnd}
					onDragCancel={handleDragCancel}
					sensors={sensors}
					id={sortableId}
				>
					<Table>
						<TableHeader className="bg-muted sticky top-0 z-10">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id}>
									{headerGroup.headers.map(header => (
										<TableHead key={header.id} colSpan={header.colSpan}>
											{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
										</TableHead>
									))}
								</TableRow>
							))}
						</TableHeader>

						<TableBody>
							{table.getRowModel().rows?.length ? (
								<SortableContext items={dataIds} strategy={verticalListSortingStrategy}>
									{table.getRowModel().rows.map(row => (
										<SortableRow
											key={row.id}
											row={row}
											onRowClick={setSelectedRowIndex}
											isSelected={selectedRowIndex === row.index}
										/>
									))}
								</SortableContext>
							) : (
								<TableRow>
									<TableCell colSpan={table.getAllLeafColumns().length} className="h-32 text-center">
										<div className="flex flex-col items-center gap-2 text-muted-foreground">
											<Plus className="h-8 w-8 opacity-50" />
											<div>
												<p className="font-medium">Nenhum campo adicionado</p>
												<p className="text-sm">{`Clique em "Adicionar campo" para começar`}</p>
											</div>
										</div>
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</DndContext>
			</div>

			{fieldOptionsToShow && <FieldOptions fieldIndex={fieldOptionsToShow.fieldIndex} control={control} field={fieldOptionsToShow.field} />}
		</div>
	);
};
