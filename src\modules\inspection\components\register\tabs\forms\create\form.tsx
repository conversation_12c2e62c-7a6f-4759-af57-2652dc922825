import { useCreateForm } from "@/modules/inspection/hooks/form/create/create-form.hook";
import { ICreateForm } from "@/modules/inspection/validators/form/create";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { Textarea } from "@/shared/components/shadcn/textarea";
import { TableCreateFormFields } from "./table";

interface IFormCreateFormProps {
	onClose: () => void;
}

export const FormCreateForm: React.FC<IFormCreateFormProps> = ({ onClose }) => {
	const { methods, addField, removeField, updateField, reorderFields, isFieldTypeOptions } = useCreateForm();

	const onSubmit = (data: ICreateForm) => {
		console.log(data);
		onClose();
	};

	return (
		<Form {...methods}>
			<form onSubmit={methods.handleSubmit(onSubmit)} className="flex flex-col gap-4">
				<FormField
					control={methods.control}
					name="title"
					render={({ field }) => (
						<FormItem>
							<FormLabel>
								Título <span className="text-red-500">*</span>
							</FormLabel>
							<FormControl>
								<Input placeholder="Digite seu título" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={methods.control}
					name="text"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Descrição</FormLabel>
							<FormControl>
								<Textarea placeholder="Digite uma descrição para o formulário" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<FormField
						control={methods.control}
						name="nomenclature"
						render={({ field }) => (
							<FormItem>
								<FormLabel>
									Nomenclatura <span className="text-red-500">*</span>
								</FormLabel>
								<FormControl>
									<Input placeholder="Digite a nomenclatura" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={methods.control}
						name="developerId"
						render={({ field }) => (
							<FormItem>
								<FormLabel>
									Elaborador <span className="text-red-500">*</span>
								</FormLabel>
								<Select onValueChange={field.onChange} value={field.value}>
									<FormControl>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Selecione um elaborador" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="Kevin Luan Damm">Kevin Luan Damm</SelectItem>
										<SelectItem value="Leonardo Lobas Rockenbach">Leonardo Lobas Rockenbach</SelectItem>
										<SelectItem value="Edi Mapepa">Edi Mapepa</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={methods.control}
						name="approverId"
						render={({ field }) => (
							<FormItem>
								<FormLabel>
									Aprovador <span className="text-red-500">*</span>
								</FormLabel>
								<Select onValueChange={field.onChange} value={field.value}>
									<FormControl>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Selecione um aprovador" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="Kevin Luan Damm">Kevin Luan Damm</SelectItem>
										<SelectItem value="Leonardo Lobas Rockenbach">Leonardo Lobas Rockenbach</SelectItem>
										<SelectItem value="Edi Mapepa">Edi Mapepa</SelectItem>
										<SelectItem value="Jheisinho Marls">Jheisinho Marls</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
				<TableCreateFormFields
					fields={methods.watch("fields") || []}
					addField={addField}
					removeField={removeField}
					updateField={updateField}
					reorderFields={reorderFields}
					isFieldTypeOptions={isFieldTypeOptions}
					control={methods.control}
				/>
				<div className="flex justify-end gap-2 pt-4">
					<Button type="button" variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button type="submit">Salvar</Button>
				</div>
			</form>
		</Form>
	);
};
