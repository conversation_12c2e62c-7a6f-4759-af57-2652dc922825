import { Modal } from "@/shared/components/custom/modal";
import { FormCreateForm } from "./form";

interface IModalCreateFormProps {
	isOpen: boolean;
	onClose: () => void;
}

export const ModalCreateForm: React.FC<IModalCreateFormProps> = ({ isOpen, onClose }) => {
	return (
		<Modal isOpen={isOpen} onClose={onClose} className="!w-[1400px] !max-w-none" title="Cadastro de Formulário">
			<FormCreateForm onClose={onClose} />
		</Modal>
	);
};
