import { Badge } from "@/shared/components/shadcn/badge";
import { Checkbox } from "@/shared/components/shadcn/checkbox";
import { ColumnDef } from "@tanstack/react-table";

export interface IFormularioFormulariosTab {
	titulo: string;
	texto: string;
	nomenclatura: string;
	revisao: string;
	aprovador: string;
	elaborador: string;
}

export const columns: ColumnDef<IFormularioFormulariosTab>[] = [
	{
		id: "select",
		header: ({ table }) => (
			<div className="flex items-center justify-center">
				<Checkbox
					checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
					onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Selecionar todos"
				/>
			</div>
		),
		cell: ({ row }) => (
			<div className="flex items-center justify-center">
				<Checkbox checked={row.getIsSelected()} onCheckedChange={value => row.toggleSelected(!!value)} aria-label="Selecionar linha" />
			</div>
		),
		enableSorting: false,
		enableHiding: false,
	},
	{
		accessorKey: "titulo",
		header: "Título",
		cell: ({ row }) => <span className="font-medium text-primary truncate max-w-[180px] block">{row.original.titulo}</span>,
	},
	{
		accessorKey: "texto",
		header: "Descrição",
		cell: ({ row }) => <span className="text-muted-foreground truncate max-w-[200px] block">{row.original.texto}</span>,
	},
	{
		accessorKey: "nomenclatura",
		header: "Nomenclatura",
		cell: ({ row }) => (
			<Badge variant="outline" className="bg-muted-foreground/10 text-xs px-2 py-1 rounded">
				{row.original.nomenclatura}
			</Badge>
		),
	},
	{
		accessorKey: "revisao",
		header: "Revisão",
		cell: ({ row }) => (
			<Badge variant="secondary" className="text-xs px-2 py-1 rounded">
				{row.original.revisao}
			</Badge>
		),
	},
	{
		accessorKey: "aprovador",
		header: "Aprovador",
		cell: ({ row }) => <span className="text-foreground font-semibold">{row.original.aprovador}</span>,
	},
	{
		accessorKey: "elaborador",
		header: "Elaborador",
		cell: ({ row }) => <span className="text-foreground">{row.original.elaborador}</span>,
	},
];
