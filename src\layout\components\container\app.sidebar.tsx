import { pathService } from "@/config/path-manager/service";
import { useUser } from "@/core/auth/hooks/user/user.hook";
import { useActiveMenuItem } from "@/layout/hooks/active-menu-item.hook";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/shared/components/shadcn/collapsible";
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarGroup,
	SidebarGroupLabel,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarMenuSub,
	SidebarMenuSubButton,
	SidebarMenuSubItem,
} from "@/shared/components/shadcn/sidebar";
import { ChevronDown } from "lucide-react";
import Link from "next/link";
import { useMemo } from "react";
import LogoMinAnimated from "../logo/logo-animated";
import { UserProfile } from "./user-profile";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
	const { ability } = useUser();
	const menuGroups = useMemo<ReadonlyArray<ReturnType<typeof pathService.getMenuForUser>[number]>>(
		() => pathService.getMenuForUser(ability),
		[ability]
	);
	const { isMenuItemActive, isSubMenuItemActive } = useActiveMenuItem();

	return (
		<Sidebar collapsible="offcanvas" {...props}>
			<SidebarHeader>
				<SidebarMenu>
					<SidebarMenuItem>
						<SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5 hover:bg-transparent">
							<LogoMinAnimated className="w-full flex h-[70px] " />
						</SidebarMenuButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarHeader>
			<SidebarContent className="hide-scrollbar overflow-y-auto">
				{menuGroups.map(group => (
					<SidebarGroup className="font-normal text-[15px] text-foreground p-2 text-start" key={group.title}>
						<SidebarGroupLabel className="text-[15px] text-foreground">{group.title}</SidebarGroupLabel>
						<SidebarMenu className="flex flex-col gap-1 items-center">
							{group.items.map(item => {
								const isActive = isMenuItemActive(item.id);
								return item.subItems && item.subItems.length > 0 ? (
									<Collapsible className="w-[255px] group" key={item.id} asChild defaultOpen={isActive}>
										<SidebarMenuItem>
											<CollapsibleTrigger
												asChild
												className={`w-[255px] h-[35px] rounded-[10px] hover:bg-hover/5 
                                                data-[state=open]:rounded-b-none 
                                                ${isActive ? "bg-hover/5" : ""}
                                                data-[state=open]:bg-hover/5`}
											>
												<SidebarMenuButton tooltip={item.name} isActive={isActive}>
													<item.icon
														size={28}
														className={`${isActive ? "text-sidebar-foreground" : "text-muted-foreground"}`}
													/>
													<span
														className={`font-light text-[18px] ${
															isActive ? "font-medium text-sidebar-foreground" : "text-muted-foreground"
														}`}
													>
														{item.name}
													</span>
													<ChevronDown className="ml-auto transition-transform duration-200 data-[state=open]:rotate-180" />
												</SidebarMenuButton>
											</CollapsibleTrigger>
											<CollapsibleContent>
												<SidebarMenuSub className="border-sidebar-border-submenu/40">
													{item.subItems.map(sub => {
														const isSubActive = isSubMenuItemActive(sub.id);
														return (
															<SidebarMenuSubItem
																className={`hover:bg-hover/3 ${isSubActive ? "bg-hover/5" : ""}`}
																key={sub.id}
															>
																<SidebarMenuSubButton asChild isActive={isSubActive}>
																	<Link href={sub.route.path}>
																		<span
																			className={`text-[15px] ${
																				isSubActive
																					? "text-sidebar-foreground font-medium"
																					: "text-muted-foreground font-light"
																			}`}
																		>
																			{sub.name}
																		</span>
																	</Link>
																</SidebarMenuSubButton>
															</SidebarMenuSubItem>
														);
													})}
												</SidebarMenuSub>
											</CollapsibleContent>
										</SidebarMenuItem>
									</Collapsible>
								) : (
									<SidebarMenuItem key={item.id}>
										<SidebarMenuButton
											asChild
											className={`w-[255px] h-[35px] rounded-[10px] hover:bg-hover/5 ${isActive ? "bg-hover/5" : ""}`}
											tooltip={item.name}
											isActive={isActive}
										>
											<Link href={item.route.path}>
												<item.icon
													size={28}
													className={`${isActive ? "text-sidebar-foreground" : "text-muted-foreground"}`}
												/>
												<span
													className={`font-light text-[18px] ${
														isActive ? "font-medium text-sidebar-foreground" : "text-muted-foreground"
													}`}
												>
													{item.name}
												</span>
											</Link>
										</SidebarMenuButton>
									</SidebarMenuItem>
								);
							})}
						</SidebarMenu>
					</SidebarGroup>
				))}
			</SidebarContent>
			<SidebarFooter>
				<UserProfile />
			</SidebarFooter>
		</Sidebar>
	);
}
