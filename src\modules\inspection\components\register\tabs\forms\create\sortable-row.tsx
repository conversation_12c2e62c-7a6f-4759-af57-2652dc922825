import { IFieldTable } from "@/modules/inspection/validators/form/create";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { flexRender, Row } from "@tanstack/react-table";
import { GripVertical } from "lucide-react";

interface SortableRowProps {
	row: Row<IFieldTable>;
	onRowClick?: (index: number) => void;
	isSelected: boolean;
}

export const SortableRow: React.FC<SortableRowProps> = ({ row, onRowClick, isSelected }) => {
	const sortableId = `field-${row.index}`;
	const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
		id: sortableId,
		animateLayoutChanges: () => false,
	});

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.5 : 1,
	};

	const handleRowClick = () => {
		onRowClick?.(row.index);
	};

	return (
		<TableRow
			ref={setNodeRef}
			style={style}
			className={`cursor-pointer ${isDragging ? "bg-muted/50" : ""} ${isSelected ? "!bg-muted" : ""}`}
			onClick={handleRowClick}
		>
			{row.getVisibleCells().map((cell, index) => (
				<TableCell key={cell.id}>
					{index === 0 ? (
						<div
							{...attributes}
							{...listeners}
							className="flex items-center justify-center cursor-grab active:cursor-grabbing p-1 rounded"
						>
							<GripVertical className="h-4 w-4 text-muted-foreground" />
						</div>
					) : (
						flexRender(cell.column.columnDef.cell, cell.getContext())
					)}
				</TableCell>
			))}
		</TableRow>
	);
};
