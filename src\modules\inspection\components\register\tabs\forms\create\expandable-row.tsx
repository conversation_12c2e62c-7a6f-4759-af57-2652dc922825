import { ICreateForm, IFieldTable } from "@/modules/inspection/validators/form/create";
import { TableBody, TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { flexRender, Row } from "@tanstack/react-table";
import { ChevronDown, ChevronRight, GripVertical } from "lucide-react";
import { Control } from "react-hook-form";
import { FieldOptions } from "./field-options";

interface ExpandableRowProps {
	row: Row<IFieldTable>;
	onRowClick?: (index: number) => void;
	isSelected: boolean;
	isFieldTypeOptions: (typeId: number) => boolean;
	control: Control<ICreateForm>;
	showFieldOptions: boolean;
}

export const ExpandableRow: React.FC<ExpandableRowProps> = ({ row, onRowClick, isSelected, isFieldTypeOptions, control, showFieldOptions }) => {
	const sortableId = `field-${row.index}`;
	const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
		id: sortableId,
		animateLayoutChanges: () => false,
	});

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.3 : 1,
	};

	const handleRowClick = () => {
		onRowClick?.(row.index);
	};

	const field = row.original;
	const shouldShowOptions = isSelected && showFieldOptions && isFieldTypeOptions(field.typeId);
	const totalColumns = row.getVisibleCells().length;
	const hasOptionsType = isFieldTypeOptions(field.typeId);

	return (
		<TableBody ref={setNodeRef} style={style} className={`${isDragging ? "relative w-full z-10" : ""}`}>
			<TableRow
				className={`cursor-pointer transition-colors ${isDragging ? "bg-transparent" : ""} ${isSelected ? "!bg-muted" : ""} ${
					shouldShowOptions ? "border-b-0" : ""
				}`}
				onClick={handleRowClick}
			>
				{row.getVisibleCells().map((cell, index) => (
					<TableCell key={cell.id} className={`${shouldShowOptions && index === 0 ? "border-b-0" : ""}`}>
						{index === 0 ? (
							<div className="flex items-center gap-2">
								<div
									{...attributes}
									{...listeners}
									className="flex items-center justify-center cursor-grab active:cursor-grabbing p-1 rounded hover:bg-muted/50"
								>
									<GripVertical className="h-4 w-4 text-muted-foreground" />
								</div>
								{hasOptionsType && (
									<div className="flex items-center justify-center">
										{shouldShowOptions ? (
											<ChevronDown className="h-4 w-4 text-muted-foreground" />
										) : (
											<ChevronRight className="h-4 w-4 text-muted-foreground" />
										)}
									</div>
								)}
							</div>
						) : (
							<div className={`${shouldShowOptions && index === totalColumns - 1 ? "border-b-0" : ""}`}>
								{flexRender(cell.column.columnDef.cell, cell.getContext())}
							</div>
						)}
					</TableCell>
				))}
			</TableRow>

			{shouldShowOptions && (
				<TableRow className={`hover:bg-transparent`}>
					<TableCell colSpan={totalColumns} className={`p-0 border-t-0`}>
						<div className={`px-4 pb-4 bg-muted/20 border-l-4 border-l-primary/20`}>
							<FieldOptions fieldIndex={row.index} control={control} field={field} />
						</div>
					</TableCell>
				</TableRow>
			)}
		</TableBody>
	);
};
