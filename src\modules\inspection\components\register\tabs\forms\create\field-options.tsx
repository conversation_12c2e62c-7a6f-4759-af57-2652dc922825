import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateForm, IFieldTable } from "@/modules/inspection/validators/form/create";
import { Button } from "@/shared/components/shadcn/button";
import { FormControl, FormField, FormItem, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Plus, Trash2 } from "lucide-react";
import { Control, useFieldArray } from "react-hook-form";

interface FieldOptionsProps {
	fieldIndex: number;
	control: Control<ICreateForm>;
	field: IFieldTable;
}

export const FieldOptions: React.FC<FieldOptionsProps> = ({ fieldIndex, control, field }) => {
	const {
		fields: optionFields,
		append,
		remove,
	} = useFieldArray({
		control,
		name: `fields.${fieldIndex}.options`,
	});

	if (field.typeId !== InspectionFormTypeEnum.OPTIONS) {
		return null;
	}

	return (
		<div className="space-y-4 p-4 border rounded-lg bg-muted/50">
			<div className="flex items-center justify-between">
				<h4 className="text-sm font-medium">Opções para {field.nickname || `Campo ${fieldIndex + 1}`}</h4>
				<Button type="button" variant="outline" size="sm" onClick={() => append({ sequence: optionFields.length + 1, option: "" })}>
					<Plus className="h-4 w-4 mr-1" />
					Adicionar Opção
				</Button>
			</div>
			{optionFields.length === 0 ? (
				<p className="text-sm text-muted-foreground">Nenhuma opção adicionada. Clique em &quot;Adicionar Opção&quot; para começar.</p>
			) : (
				<div className="space-y-2">
					{optionFields.map((option, optionIndex) => (
						<div key={option.id} className="flex gap-2 items-start">
							<div className="flex-1">
								<FormField
									control={control}
									name={`fields.${fieldIndex}.options.${optionIndex}.option`}
									render={({ field }) => (
										<FormItem>
											<FormControl>
												<Input placeholder={`Opção ${optionIndex + 1}`} {...field} value={field.value || ""} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
							<Button
								type="button"
								variant="ghost"
								size="sm"
								onClick={() => remove(optionIndex)}
								className="h-9 w-9 p-0 text-destructive hover:text-destructive"
							>
								<Trash2 className="h-4 w-4" />
								<span className="sr-only">Remover opção</span>
							</Button>
						</div>
					))}
				</div>
			)}
		</div>
	);
};
