"use client";

import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/shared/components/shadcn/tabs";
import type { LucideIcon } from "lucide-react";
import { Circle, Component, FileText, Grid, Link, Plus, Ruler, Search, Users } from "lucide-react";
import { useState } from "react";
import { CamposTab } from "./tabs/campos-tab";
import { ColaboradoresTab } from "./tabs/colaboradores-tab";
import { ModalCreateForm } from "./tabs/forms/create/modal";
import { FormulariosTab } from "./tabs/forms/list/table";
import { MedidasTab } from "./tabs/medidas-tab";

interface ITabItemRegisterInspectionTabs {
	value: string;
	label: string;
	icon: LucideIcon;
	renderContent: (searchTerm: string) => React.ReactNode;
	onNew: (searchTerm: string) => void;
	disabled?: boolean;
}

export const RegisterInspectionTabs = () => {
	const [activeTab, setActiveTab] = useState("medidas");
	const [searchTerm, setSearchTerm] = useState("");
	const [isFormModalOpen, setFormModalOpen] = useState(false);

	const tabItems: ITabItemRegisterInspectionTabs[] = [
		{
			value: "medidas",
			label: "Medidas",
			icon: Ruler,
			renderContent: searchTerm => <MedidasTab searchTerm={searchTerm} />,
			onNew: searchTerm => {
				console.log("Novo em Medidas", searchTerm);
			},
		},
		{
			value: "campos",
			label: "Campos",
			icon: Grid,
			renderContent: searchTerm => <CamposTab searchTerm={searchTerm} />,
			onNew: searchTerm => {
				console.log("Novo em Campos", searchTerm);
			},
		},
		{
			value: "vinculo-colaboradores",
			label: "Vínculo de colaboradores",
			icon: Users,
			renderContent: searchTerm => <ColaboradoresTab searchTerm={searchTerm} />,
			onNew: searchTerm => {
				console.log("Novo em Vínculo de colaboradores", searchTerm);
			},
		},
		{
			value: "formularios",
			label: "Formulários",
			icon: FileText,
			renderContent: searchTerm => <FormulariosTab searchTerm={searchTerm} />,
			onNew: () => {
				setFormModalOpen(true);
			},
		},
		{
			value: "celulas",
			label: "Células",
			icon: Circle,
			renderContent: searchTerm => <div>Células content goes here. Termo: {searchTerm}</div>,
			onNew: searchTerm => {
				console.log("Novo em Células", searchTerm);
			},
			disabled: false,
		},
		{
			value: "componentes",
			label: "Componentes",
			icon: Component,
			renderContent: searchTerm => <div>Componentes content goes here. Termo: {searchTerm}</div>,
			onNew: searchTerm => {
				console.log("Novo em Componentes", searchTerm);
			},
			disabled: false,
		},
		{
			value: "vinculos",
			label: "Vínculos",
			icon: Link,
			renderContent: searchTerm => <div>Vínculos content goes here. Termo: {searchTerm}</div>,
			onNew: searchTerm => {
				console.log("Novo em Vínculos", searchTerm);
			},
			disabled: false,
		},
	];

	const activeTabItem = tabItems.find(tab => tab.value === activeTab);

	return (
		<div className="w-full">
			<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
				<div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3 mb-4">
					<TabsList className="flex flex-wrap gap-2 bg-transparent p-0 rounded-none w-full md:w-auto">
						{tabItems.map(({ value, label, icon: Icon, disabled }) => (
							<TabsTrigger
								key={value}
								value={value}
								disabled={disabled}
								className={`rounded-controls px-3 py-2 flex items-center gap-2 transition-colors text-sm md:text-base
									data-[state=active]:bg-primary data-[state=active]:text-white data-[state=active]:shadow
									bg-secondary text-foreground
									${
										disabled
											? "opacity-60 cursor-not-allowed border border-dashed border-gray-300 bg-gray-100 text-gray-400 hover:bg-gray-100"
											: "hover:bg-primary/10 hover:text-primary"
									}
								`}
								tabIndex={disabled ? -1 : 0}
								aria-disabled={disabled}
								title={disabled ? "Funcionalidade em breve" : label}
							>
								<Icon className="size-4 mr-2" />
								{label}
							</TabsTrigger>
						))}
					</TabsList>
					<div className="flex flex-col sm:flex-row items-stretch gap-2 w-full md:w-auto md:items-center md:gap-3 mt-2 md:mt-0">
						<div className="relative w-full sm:w-64">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground size-4" />
							<Input
								placeholder="Pesquisar..."
								value={searchTerm}
								onChange={e => setSearchTerm(e.target.value)}
								className="pl-10 w-full"
							/>
						</div>
						<Button
							onClick={() => activeTabItem?.onNew(searchTerm)}
							className="bg-primary hover:bg-primary/80 text-white flex items-center gap-2 w-full sm:w-auto"
						>
							<Plus className="size-4" />
							Novo
						</Button>
					</div>
				</div>
				{tabItems.map(({ value, renderContent }) => (
					<TabsContent key={value} value={value} className="mt-6">
						{renderContent(searchTerm)}
					</TabsContent>
				))}
			</Tabs>
			{/* Modal de Novo Formulário */}
			{/* {activeTab === "formularios" && <FormModal isOpen={isFormModalOpen} onClose={() => setFormModalOpen(false)} />} */}

			<ModalCreateForm isOpen={true} onClose={() => setFormModalOpen(false)} />
		</div>
	);
};
